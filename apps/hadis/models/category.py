from dj_category.models import BaseCategoryAbstract
from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import FilerImageField

from dj_language.models import Language
class CategoryTagLevel(models.Model):
    tag = models.ForeignKey("tag.Tag", on_delete=models.CASCADE, verbose_name=_('tag'))
    category = models.ForeignKey("hadis.Category", on_delete=models.CASCADE, )
    level = models.PositiveSmallIntegerField(default=1, verbose_name=_('level'))

    class Meta:
        unique_together = ('tag', 'category', 'level')
        db_table = 'hadis_category_tags'


class Category(BaseCategoryAbstract):
    content_type = None
    language = None
    thumbnail = FilerImageField(related_name='+', on_delete=models.PROTECT, null=True, blank=True)
    tags = models.ManyToManyField('tag.Tag', related_name='categories', through=CategoryTagLevel)
    name = models.JSONField(default=dict, verbose_name=_('name'))
    slug = None

    class Meta:
        verbose_name = _('Hadis Category')
        verbose_name_plural = _('Hadis Categories')
        
    def __str__(self):
        for lang in ['fa', 'en', 'ar']:
            for tr in self.name:
                if tr['language_code'] == lang:
                    return tr['title']

        return self.name[0]['title']

    def get_translation(self, lang, fallback='en'):
        for tr in self.name:
            if tr['language_code'] == lang:
                return tr['title']

        if fallback:
            for tr in self.name:
                if tr['language_code'] == fallback:
                    return tr['title']

        return str(self)
